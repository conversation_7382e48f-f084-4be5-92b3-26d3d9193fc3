[http_server]
  Environment = "test"
  ServeAddr = ":10002"
  InterAddr = ":10001"
  PProfAddr = ":10188"
  Debug = true
  AllowOrigins = ["*"]

[Mysql]
  [Mysql.audit]
    [Mysql.audit.RW]
      DataSourceName = "root:12345678@tcp(127.0.0.1:3306)/audit?charset=utf8mb4&timeout=200ms&loc=Local&parseTime=True"
      MaxIdleConns = 100
      MaxOpenConns = 200
      MaxRetryTimes = 1
    [Mysql.audit.R]
      DataSourceName = "root:12345678@tcp(127.0.0.1:3306)/audit?charset=utf8mb4&timeout=200ms&loc=Local&parseTime=True"
      MaxIdleConns = 100
      MaxOpenConns = 200
      MaxRetryTimes = 1

[Redis]
  [Redis.default]
    Servers = ["127.0.0.1:6379"]
    MaxRetries = -1
    Cluster = false

[Log]
  LogLevel = "info"
  LogFile = "/Users/<USER>/logs/audit.log"
  KafkaLog = false
  LocalLog = true
  [Log.Kafka]
    Servers = ["127.0.0.1:9092"]
    Topic = "audit-log"
    InjectHostname = true
    App = "audit"
    AppName = "audit-dev"
    EnvName = "dev"
  [Log.Rotate]
    Interval = 24
    MaxAge = 30
    MaxSize = 15360
    LocalTime = true

[Cas]
  EndPoint = "http://localhost"
  FrontDomain = "http://localhost"
  ClientID = "0806f3ed5248d8695029"
  ClientSecret = "52f4468d7660beb3bf24c918e1e689b22ad9dffc"
  Cert = "-----BEGIN CERTIFICATE-----\nMIIE3TCCAsWgAwIBAgIDAeJAMA0GCSqGSIb3DQEBCwUAMCgxDjAMBgNVBAoTBWFk\nbWluMRYwFAYDVQQDEw1jZXJ0LWJ1aWx0LWluMB4XDTI1MDIyMjEwMjkzNloXDTQ1\nMDIyMjEwMjkzNlowKDEOMAwGA1UEChMFYWRtaW4xFjAUBgNVBAMTDWNlcnQtYnVp\nbHQtaW4wggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQDhGeozEJbCJN0v\nOrSatmwajjaIURHVhbdXFePFP9//yXIpSvWZzz3A+sZO4x+0yJyc/4/+QleWu/Dw\nxIAcsvzHLasDu7heNXcXXP+6AMW8ZJNRktM3Ua+ZIOow2/9rPRoNUhPJRT4pSY8H\ni9dGZNhanbP1dAYWPCjrnfcun5TPBnFOvUq8BDu+BLh7Tw03F999bsNCFEPfaaq8\n9sYWEcuKNGJz2UrfgFz5XfMpfgVoUg0+BoZwA87MV1p2XC8ovpdou206Ljuje6Xf\n6uzuB6uRFehA/sZ9F9M569aN5RmmbuE48BkIpgSCUUKTbGqt9MTGeM72Pu+x5R3V\n7DYJctOzo9H85amo3Lllq2FJkkJWwjzeQLRhjS8+srPmDVWwvN6+kIuT1jn18i6+\nqNUWBBDEzbYGvrRp2Wn4baFSaLnCKwcxRvlF7nyy7CXku9bbSLhsm39b8fsiPv2X\n2u3topqRuibHMCmaOF22siyBgBM47pANiGVZ7jeE9N82+LtQQYgciLgevZYtOh79\nV1Iq40Di46juTvtz8zeRT+X9zNIbvJCmiPGIX8Xci+AGO0Bcdv7EUffim8twplAC\n/jZQmqVRS0bUOzaKYq3i0DTSsV7XYQJ94uwmVDUZMVhKWYCXF4zHGgjtby+BqQAO\n3OT5Z2A6wjRGb1Y+1ad7QKlGc2UYeQIDAQABoxAwDjAMBgNVHRMBAf8EAjAAMA0G\nCSqGSIb3DQEBCwUAA4ICAQBqaugfHUpg34sw/aX7c2bsEPriQ3yCKPWpdmr9W31E\n0AMBXj4gEbZBXAxFdFNYhDaqD38J1MxVU4ZAmcNv/UIX6BIceUU7sZuPx16HLXT4\nU8FcdqY9FXRGpqgbGK2h+Lx6w9gvsX7wZvGasBQvxmaPWbI9fzNdLTF96A/FNP3w\naZwILhiKeG+E9Hh+9s27c8VQtH59uCTAsMmEb9fJopZVE+CYVOdW3cfqGXjLlqBP\nkOHR4il9Q01X3SvYE5DxydidYNdLN1/+Ows6emLVuLTQHuraYGLc0RJ/efYfZ9iH\nnp7/p5ZckZl1u2sRWF0EoPmmcPlHYn3UKouQMnDw8ENL0tfYnS7DNFZ2Xt0WT8Pe\nyMYN8AvFnQcVS9WQhVuTCAlp5hgEpsN7WWr7WsPpBILplm7tAegjsne4wa4gKU2p\nRFLA4t96V54+XpFhFYEZc/Wqyf3RAK29Cy1mmmaiwcK7NTUZGhv3UPw6T6zlHRS3\n2rn4/3hVS+lPcJWikFzzTeQ3t7dL6ls88FM+8hyPP7cWgJuo23uVh6m4TpMO7yzC\ns5pOpiKDWAL5IsBgzydqrfcHxpQkwoP7DDdCsCBRd0q0CPJx7Ug2qTdXvco7GAvI\noVk1a7WeA0PCPyNwXARpDITdZlTHv5cWcornM5VD1FPYlCRgY+6mIJ2irC34x/Wc\nYw==\n-----END CERTIFICATE-----\n"
  OrgName = "built-in"
  AppName = "audit"
  EnforcerID = ""
  LogoutPath = "/api/user/logout"
  CallbackPath = "/api/user/callback"
  NoValidateSSOPath = ["/"]
  NoValidateIAMPath = ["/"]
  CookieKey = "_q"
  SessionKey = "audit_session_%s"
  SessionTTL = 86400

[WebAuthn]
  RPDisplayName = "audit"
  RPID = "localhost"
  RPOrigins = ["localhost"]

[Notify]
  [Notify.alarm]
    TeamsWebhookUrl = "https://prod-13.southeastasia.logic.azure.com:443/workflows/77852765f7cf4a37bc70df4f09ec437e/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=N7GMiC0ZGcit3nay-TpWy_b_USbuHvwqqCRcxIjzB9k"
    FeishuWebhookUrl = "https://open.feishu.cn/open-apis/bot/v2/hook/12bf673b-2599-4faa-8861-7d775b7f67ac"
    SmsNumbers = ["+8618519930516"]
    Interval = 180
