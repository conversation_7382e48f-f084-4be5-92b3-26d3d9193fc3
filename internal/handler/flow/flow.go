/**
 * @note
 * workflow
 *
 * <AUTHOR>
 * @date 	2025-07-02
 */
package flow

import (
	"github.com/kataras/iris/v12"

	flowLogic "gitlab.docsl.com/security/audit/internal/logic/flow"
	auditCommon "gitlab.docsl.com/security/audit/pkg/common"
	. "gitlab.docsl.com/security/common"
)

func TriggerAuditFlow(ctx iris.Context) {
	req := &TriggerFlowRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	var err error
	flowInstanceItem, err := flowLogic.TriggerAuditFlow(ctx, req.FlowName, req.FlowInstanceID, req.NodeName, req.Data)
	if err != nil {
		SetRet(ctx, NewErrorWithMessage(auditCommon.ErrTriggerWorkflow, err.Error()).SetDesc(flowInstanceItem))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(flowInstanceItem))
}

func 