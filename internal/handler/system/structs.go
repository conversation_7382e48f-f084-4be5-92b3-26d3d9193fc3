/**
 * @note
 * system handler structs - now using logic layer structs
 *
 * <AUTHOR>
 * @date 	2025-07-04
 */
package system

import "gitlab.docsl.com/security/audit/internal/logic/system"

// ListSystemRequest 查询系统列表请求
type ListSystemRequest struct {
	Page       int    `json:"page" validate:"gte=0"`
	PerPage    int    `json:"perPage" validate:"gt=0"`
	SystemID   string `json:"systemID"`
	SystemName string `json:"systemName"`
	KeyType    string `json:"keyType"`
	Order      string `json:"order"`
}

// ListSystemResponse 查询系统列表响应
type ListSystemResponse struct {
	Total int64                `json:"total"`
	List  []*system.SystemItem `json:"list"`
}

// CreateSystemRequest 创建系统请求
type CreateSystemRequest struct {
	SystemID    string `json:"systemID" validate:"required"`
	SystemName  string `json:"systemName" validate:"required"`
	Description string `json:"description"`
	KeyType     string `json:"keyType" validate:"required"`
	PublicKey   string `json:"publicKey" validate:"required"`
}

// CreateSystemResponse 创建系统响应
type CreateSystemResponse struct {
	ID uint `json:"id"`
}

// UpdateSystemRequest 更新系统请求
type UpdateSystemRequest struct {
	SystemID    string  `json:"systemID" validate:"required"`
	SystemName  *string `json:"systemName"`
	Description *string `json:"description"`
	KeyType     *string `json:"keyType"`
	PublicKey   *string `json:"publicKey"`
}

// DeleteSystemRequest 删除系统请求
type DeleteSystemRequest struct {
	ID uint `json:"id" validate:"required"`
}

// GetSystemRequest 获取系统详情请求
type GetSystemRequest struct {
	ID uint `json:"id" validate:"required"`
}
