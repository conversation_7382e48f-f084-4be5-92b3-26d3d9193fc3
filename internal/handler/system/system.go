/**
 * @note
 * system handler
 *
 * <AUTHOR>
 * @date 	2025-07-04
 */
package system

import (
	"github.com/kataras/iris/v12"

	systemLogic "gitlab.docsl.com/security/audit/internal/logic/system"
	auditCommon "gitlab.docsl.com/security/audit/pkg/common"
	. "gitlab.docsl.com/security/common"
)

// ListSystems 查询系统列表
func ListSystems(ctx iris.Context) {
	req := &ListSystemRequest{}
	if err := ctx.ReadQuery(req); err != nil {
		SetRet(ctx, NewError(ErrCodeParam, err))
		return
	}

	// 调用logic层处理业务逻辑
	response, err := systemLogic.ListSystems(ctx, req)
	if err != nil {
		SetRet(ctx, NewError(auditCommon.ErrQuerySystemList, err))
		return
	}

	SetRet(ctx, NewError(ErrCodeOK).SetDesc(response))
}

// CreateSystem 创建系统
func CreateSystem(ctx iris.Context) {
	req := &CreateSystemRequest{}
	if err := ctx.ReadJSON(req); err != nil {
		SetRet(ctx, NewError(ErrCodeJson, err))
		return
	}

	if err := Validator.Struct(req); err != nil {
		SetRet(ctx, NewError(ErrCodeParam, err))
		return
	}

	// 调用logic层处理业务逻辑
	response, err := systemLogic.CreateSystem(ctx, req.SystemID, req.SystemName,
		req.Description, req.KeyType, req.PublicKey)
	if err != nil {
		SetRet(ctx, NewError(auditCommon.ErrCreateSystem, err))
		return
	}

	SetRet(ctx, NewError(ErrCodeOK).SetDesc(response))
}

// UpdateSystem 更新系统
func UpdateSystem(ctx iris.Context) {
	req := &UpdateSystemRequest{}
	if err := ctx.ReadJSON(req); err != nil {
		SetRet(ctx, NewError(ErrCodeJson, err))
		return
	}

	if err := Validator.Struct(req); err != nil {
		SetRet(ctx, NewError(ErrCodeParam, err))
		return
	}

	// 调用logic层处理业务逻辑
	err := systemLogic.UpdateSystem(ctx, req.SystemID, req.SystemName, req.Description, req.KeyType, req.PublicKey)
	if err != nil {
		SetRet(ctx, NewErrorWithMessage(auditCommon.ErrUpdateSystem, err.Error()))
		return
	}

	SetRet(ctx, NewError(ErrCodeOK))
}

// DeleteSystem 删除系统
func DeleteSystem(ctx iris.Context) {
	req := &DeleteSystemRequest{}
	if err := ctx.ReadJSON(req); err != nil {
		SetRet(ctx, NewError(ErrCodeJson, err))
		return
	}

	if err := Validator.Struct(req); err != nil {
		SetRet(ctx, NewError(ErrCodeParam, err))
		return
	}

	// 调用logic层处理业务逻辑
	err := systemLogic.DeleteSystem(ctx, req)
	if err != nil {
		SetRet(ctx, NewErrorWithMessage(auditCommon.ErrDeleteSystem, err.Error()))
		return
	}

	SetRet(ctx, NewError(ErrCodeOK))
}
