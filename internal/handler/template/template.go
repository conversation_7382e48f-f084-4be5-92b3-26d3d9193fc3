/**
 * @note
 * template handler
 *
 * <AUTHOR>
 * @date 	2025-07-04
 */
package template

import (
	"github.com/kataras/iris/v12"

	tLogic "gitlab.docsl.com/security/audit/internal/logic/template"
	auditCommon "gitlab.docsl.com/security/audit/pkg/common"
	. "gitlab.docsl.com/security/common"
)

// QueryWorkflowTemplates 查询流程模板列表
func QueryWorkflowTemplates(ctx iris.Context) {
	req := &QueryWorkflowTemplateRequest{}
	if err := ctx.ReadQuery(req); err != nil {
		SetRet(ctx, NewError(ErrCodeParam, err))
		return
	}

	if err := Validator.Struct(req); err != nil {
		SetRet(ctx, NewError(ErrCodeParam, err))
		return
	}

	// 调用logic层
	items, total, err := tLogic.QueryWorkflowTemplates(ctx, req.<PERSON>, req.PerPage)
	if err != nil {
		SetRet(ctx, NewError(auditCommon.ErrQueryTemplateList, err))
		return
	}

	// 构造响应
	response := &QueryWorkflowTemplateResponse{
		Total: total,
		Items: items,
	}

	SetRet(ctx, NewError(ErrCodeOK).SetDesc(response))
}

// CreateWorkflowTemplate 创建流程模板
func CreateWorkflowTemplate(ctx iris.Context) {
	req := &CreateWorkflowTemplateRequest{}
	if err := ctx.ReadJSON(req); err != nil {
		SetRet(ctx, NewError(ErrCodeJson, err))
		return
	}

	if err := Validator.Struct(req); err != nil {
		SetRet(ctx, NewError(ErrCodeParam, err))
		return
	}

	// 调用logic层
	workflowTemplateID, err := tLogic.CreateWorkflowTemplate(ctx, req.WorkflowName, req.Description, req.NodeTemplates)
	if err != nil {
		SetRet(ctx, NewError(auditCommon.ErrCreateTemplate, err))
		return
	}

	// 构造响应
	response := &CreateWorkflowTemplateResponse{
		WorkflowTemplateID: workflowTemplateID,
	}

	SetRet(ctx, NewError(ErrCodeOK).SetDesc(response))
}

// UpdateWorkflowTemplate 更新流程模板
func UpdateWorkflowTemplate(ctx iris.Context) {
	req := &UpdateWorkflowTemplateRequest{}
	if err := ctx.ReadJSON(req); err != nil {
		SetRet(ctx, NewError(ErrCodeJson, err))
		return
	}

	if err := Validator.Struct(req); err != nil {
		SetRet(ctx, NewError(ErrCodeParam, err))
		return
	}

	// 调用logic层
	workflowTemplateID, err := tLogic.UpdateWorkflowTemplate(ctx, req.WorkflowName, req.Description, req.NodeTemplates)
	if err != nil {
		SetRet(ctx, NewError(auditCommon.ErrUpdateTemplate, err))
		return
	}

	// 构造响应
	response := &UpdateWorkflowTemplateResponse{
		WorkflowTemplateID: workflowTemplateID,
	}

	SetRet(ctx, NewError(ErrCodeOK).SetDesc(response))
}

// DeleteWorkflowTemplate 删除流程模板
func DeleteWorkflowTemplate(ctx iris.Context) {
	req := &DeleteWorkflowTemplateRequest{}
	if err := ctx.ReadJSON(req); err != nil {
		SetRet(ctx, NewError(ErrCodeJson, err))
		return
	}

	if err := Validator.Struct(req); err != nil {
		SetRet(ctx, NewError(ErrCodeParam, err))
		return
	}

	// 调用logic层
	err := tLogic.DeleteWorkflowTemplate(ctx, req.WorkflowName)
	if err != nil {
		SetRet(ctx, NewError(auditCommon.ErrDeleteTemplate, err))
		return
	}

	SetRet(ctx, NewError(ErrCodeOK))
}
