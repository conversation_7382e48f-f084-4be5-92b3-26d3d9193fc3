package template

import (
	"testing"

	tLogic "gitlab.docsl.com/security/audit/internal/logic/template"
	flowModel "gitlab.docsl.com/security/audit/internal/model/flow"
)

func TestQueryWorkflowTemplateRequest(t *testing.T) {
	// 测试查询请求结构体
	req := &ListWorkflowTemplateRequest{
		Page:    1,
		PerPage: 10,
	}

	if req.Page != 1 {
		t.<PERSON><PERSON>("Expected Page 1, got %d", req.Page)
	}

	if req.PerPage != 10 {
		t.<PERSON><PERSON><PERSON>("Expected PerPage 10, got %d", req.PerPage)
	}
}

func TestCreateWorkflowTemplateRequest(t *testing.T) {
	// 测试创建请求结构体
	req := &CreateWorkflowTemplateRequest{
		WorkflowName: "test-workflow",
		Description:  "Test description",
		NodeTemplates: []*tLogic.NodeTemplateItem{
			{
				NodeName:    "node1",
				Description: "First node",
				NodeIndex:   0,
				SystemID:    "system1",
				Config:      &flowModel.NodeConfig{},
			},
		},
	}

	if req.WorkflowName != "test-workflow" {
		t.<PERSON><PERSON><PERSON>("Expected WorkflowName 'test-workflow', got %s", req.WorkflowName)
	}

	if len(req.NodeTemplates) != 1 {
		t.Errorf("Expected 1 node template, got %d", len(req.NodeTemplates))
	}

	if req.NodeTemplates[0].NodeName != "node1" {
		t.Errorf("Expected NodeName 'node1', got %s", req.NodeTemplates[0].NodeName)
	}
}

func TestUpdateWorkflowTemplateRequest(t *testing.T) {
	// 测试更新请求结构体
	description := "Updated description"
	req := &UpdateWorkflowTemplateRequest{
		WorkflowName: "test-workflow",
		Description:  &description,
		NodeTemplates: []*tLogic.NodeTemplateItem{
			{
				NodeName:    "node1",
				Description: "Updated node",
				NodeIndex:   0,
				SystemID:    "system1",
				Config:      &flowModel.NodeConfig{},
			},
		},
	}

	if req.WorkflowName != "test-workflow" {
		t.Errorf("Expected WorkflowName 'test-workflow', got %s", req.WorkflowName)
	}

	if req.Description == nil || *req.Description != "Updated description" {
		t.Errorf("Expected Description 'Updated description', got %v", req.Description)
	}

	if len(req.NodeTemplates) != 1 {
		t.Errorf("Expected 1 node template, got %d", len(req.NodeTemplates))
	}
}

func TestDeleteWorkflowTemplateRequest(t *testing.T) {
	// 测试删除请求结构体
	req := &DeleteWorkflowTemplateRequest{
		WorkflowName: "test-workflow",
	}

	if req.WorkflowName != "test-workflow" {
		t.Errorf("Expected WorkflowName 'test-workflow', got %s", req.WorkflowName)
	}
}

func TestCreateWorkflowTemplateResponse(t *testing.T) {
	// 测试创建响应结构体
	resp := &CreateWorkflowTemplateResponse{
		WorkflowTemplateID: 123,
	}

	if resp.WorkflowTemplateID != 123 {
		t.Errorf("Expected WorkflowTemplateID 123, got %d", resp.WorkflowTemplateID)
	}
}

func TestUpdateWorkflowTemplateResponse(t *testing.T) {
	// 测试更新响应结构体
	resp := &UpdateWorkflowTemplateResponse{
		WorkflowTemplateID: 456,
	}

	if resp.WorkflowTemplateID != 456 {
		t.Errorf("Expected WorkflowTemplateID 456, got %d", resp.WorkflowTemplateID)
	}
}

func TestQueryWorkflowTemplateResponse(t *testing.T) {
	// 测试查询响应结构体
	items := []*tLogic.WorkflowTemplateItem{
		{
			WorkflowTemplateID: 1,
			WorkflowName:       "test-workflow",
			Description:        "Test description",
			NodeTemplates: []*tLogic.NodeTemplateItem{
				{
					NodeTemplateID: 1,
					NodeName:       "node1",
					Description:    "First node",
					NodeIndex:      0,
					SystemID:       "system1",
					Config:         &flowModel.NodeConfig{},
				},
			},
		},
	}

	resp := &ListWorkflowTemplateResponse{
		Total: 1,
		Items: items,
	}

	if resp.Total != 1 {
		t.Errorf("Expected Total 1, got %d", resp.Total)
	}

	if len(resp.Items) != 1 {
		t.Errorf("Expected 1 item, got %d", len(resp.Items))
	}

	if resp.Items[0].WorkflowName != "test-workflow" {
		t.Errorf("Expected WorkflowName 'test-workflow', got %s", resp.Items[0].WorkflowName)
	}
}
