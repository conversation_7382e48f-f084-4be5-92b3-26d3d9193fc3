/**
 * @note
 * workflow
 *
 * <AUTHOR>
 * @date 	2025-07-01
 */
package flow

import (
	"context"
	"fmt"
	"sort"

	"gorm.io/gorm"

	workflowModel "gitlab.docsl.com/security/audit/internal/model/flow"
	auditCommon "gitlab.docsl.com/security/audit/pkg/common"
	"gitlab.docsl.com/security/common"
)

/*
TriggerAuditFlow
不仅是创建一个Flow，而且是后续推进flow的方法，二合一
如果只有flowName、nodeName，没有flowInstanceID，则是创建flow，如果有flowInstanceID，则是推动已有flow进展。
*/
func TriggerAuditFlow(ctx context.Context,
	flowName string, flowInstanceID int64, nodeName string, data interface{}) (instance *FlowInstanceItem, err error) {
	if flowInstanceID == 0 {
		return StartAuditFlow(ctx, flowName, nodeName, data)
	} else {
		return AdvanceAuditFlow(ctx, flowInstanceID, nodeName, data)
	}
}

/*
AdvanceAuditFlow
推进flow的逻辑：

	1.根据flowInstanceID，查询对应的workflowRuntime和对应workflowRuntime的所有的nodeRuntime。
	2.根据workflowRuntime的templateID，调用GetWorkflowAndNodeTmplByFlowTemplateID，查询workflowTemplate和对应的nodeTemplates。
	3.校验传入的nodeName找到对应的nodeRuntime，且对应的nodeRuntime的systemID是否和这个系统的systemID对得上（systemID通过context.Value(auditCommon.KeySystem)获取），如果不是，说明入参非法。
	4.根据nodeRuntime的nodeTemplateID，找到第二步里查到的nodeTemplate，根据对应的nodeTemplate的nodeConfig，调用对应的ValidateSchema方法，校验data是否符合要求，如果不符合，返回错误。
	5.再根据nodeConfig里的rule，校验当前节点的data是否符合要求，这里校验时需要把本节点，以及前序所有节点的data都set进一个data map里，key是对应的nodeName，value是对应节点的data，最后再调用ValidateData进行校验，如果出错就说明数据非法。
	6.再根据nodeConfig里的topology，校验当前节点的前置节点情况，需要根据topology的配置来校验。
	7.如果到这里还没出问题，则说明数据合法，此时将nodeRuntime的状态设置为auditCommon.NodeStatePassed，如果是最后一个节点，则需要开启事务，在同一个事务里，同时将workflowRuntime的状态设置为auditCommon.WorkflowStatePassed。
	8.最后返回flowInstanceID。
*/
func AdvanceAuditFlow(ctx context.Context, flowName string,
	flowInstanceID string, nodeName string, data interface{}) (instance *FlowInstanceItem, err error) {

	// 1. 根据flowInstanceID查询对应的workflowRuntime和所有nodeRuntime
	workflowRuntime, err := workflowModel.GetWorkflowRuntimeByNameAndInstanceID(ctx, flowName, flowInstanceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get flow runtime for ID %d: %v", flowInstanceID, err)
	} else if workflowRuntime.State == auditCommon.WorkflowStatePass || workflowRuntime.State == auditCommon.WorkflowStateFailed {
		return nil, fmt.Errorf("the flow [%d] is in ended", flowInstanceID)
	}

	// 查询所有nodeRuntime
	pid := int64(workflowRuntime.ID)
	nodeRuntimeFilter := workflowModel.QueryNodeRuntimeFilter{
		WorkflowPrimaryID: &pid,
	}
	nodeRuntimes, err := workflowModel.QueryNodeRuntimesBySeveralConditions(ctx, nodeRuntimeFilter)
	if err != nil {
		return nil, fmt.Errorf("failed to get node runtimes for flow instance ID %s: %v", flowInstanceID, err)
	}

	if len(nodeRuntimes) == 0 {
		return nil, fmt.Errorf("no node runtimes found for flow instance ID %s", flowInstanceID)
	}

	// 按 nodeIndex 排序
	sort.Slice(nodeRuntimes, func(i, j int) bool {
		return nodeRuntimes[i].NodeIndex < nodeRuntimes[j].NodeIndex
	})

	// 2. 根据workflowRuntime的templateID查询workflowTemplate和nodeTemplates
	_, nodeTemplates, err := workflowModel.GetWorkflowAndNodeTmplByFlowTemplateID(ctx, workflowRuntime.WorkflowTemplateID)
	if err != nil {
		return nil, fmt.Errorf("failed to get flow template and node templates: %v", err)
	}

	// 3. 校验传入的nodeName找到对应的nodeRuntime
	var currentNodeRuntime *workflowModel.AuditNodeRuntimeTable
	for _, nodeRuntime := range nodeRuntimes {
		if nodeRuntime.NodeName == nodeName {
			currentNodeRuntime = nodeRuntime
			break
		}
	}

	if currentNodeRuntime == nil {
		return nil, fmt.Errorf("node '%s' not found in flow instance %s", nodeName, flowInstanceID)
	} else if currentNodeRuntime.State != auditCommon.NodeStateInit {
		return nil, fmt.Errorf("node '%s' is not in init state", nodeName)
	}

	// 校验systemID是否匹配
	systemIDValue := ctx.Value(auditCommon.KeySystem)
	if systemIDValue == nil {
		return nil, fmt.Errorf("system ID not found in context")
	}
	systemIDStr, ok := systemIDValue.(string)
	if !ok {
		return nil, fmt.Errorf("invalid system ID type in context")
	}

	if currentNodeRuntime.SystemID != systemIDStr {
		return nil, fmt.Errorf("system ID mismatch: expected '%s', got '%s'", currentNodeRuntime.SystemID, systemIDStr)
	}

	// 4. 根据nodeRuntime的nodeTemplateID找到对应的nodeTemplate
	var currentNodeTemplate *workflowModel.AuditNodeTemplateTable
	for _, nodeTemplate := range nodeTemplates {
		if int64(nodeTemplate.ID) == currentNodeRuntime.NodeTemplateID {
			currentNodeTemplate = nodeTemplate
			break
		}
	}

	if currentNodeTemplate == nil {
		return nil, fmt.Errorf("node template not found for node template ID %d", currentNodeRuntime.NodeTemplateID)
	}

	// 4. 进入schema校验阶段，从这里开始无论成功失败都要记录完整流程
	var validationError error
	nodeState := auditCommon.NodeStatePass
	var workflowState auditCommon.WorkflowState

	// Schema校验
	if currentNodeTemplate.Config != nil && currentNodeTemplate.Config.Schema != nil {
		if err := currentNodeTemplate.Config.ValidateSchema(data); err != nil {
			validationError = fmt.Errorf("data validation failed against schema: %v", err)
			nodeState = auditCommon.NodeStateFailed
		}
	}

	// 5. 根据nodeConfig的rules校验data（只有schema校验通过才继续）
	if validationError == nil && currentNodeTemplate.Config != nil && len(currentNodeTemplate.Config.Rules) > 0 {
		// 构造数据map（包含当前节点和前序所有已完成节点的数据）
		dataMap := make(map[string]interface{})

		// 添加前序节点的数据
		for _, nodeRuntime := range nodeRuntimes {
			if nodeRuntime.NodeIndex < currentNodeRuntime.NodeIndex && nodeRuntime.State == auditCommon.NodeStatePass {
				if nodeRuntime.Data != nil && nodeRuntime.Data.Data != nil {
					dataMap[nodeRuntime.NodeName] = nodeRuntime.Data.Data
				}
			}
		}

		// 添加当前节点的数据
		dataMap[nodeName] = data

		// 校验数据
		if err := currentNodeTemplate.Config.ValidateData(dataMap); err != nil {
			validationError = fmt.Errorf("data validation failed against rules: %v", err)
			nodeState = auditCommon.NodeStateFailed
		}
	}

	// 6. 根据nodeConfig的topology校验前置节点情况（只有前面校验通过才继续）
	if validationError == nil && currentNodeTemplate.Config != nil && currentNodeTemplate.Config.Topology != nil {
		if err := currentNodeTemplate.Config.Topology.Validate(nodeName, nodeRuntimes); err != nil {
			validationError = fmt.Errorf("topology validation failed: %v", err)
			nodeState = auditCommon.NodeStateFailed
		}
	}

	// 7. 更新nodeRuntime状态（无论校验成功失败都要记录）
	nodeData := &workflowModel.NodeData{
		Data: data,
		Error: func() string {
			if validationError != nil {
				return validationError.Error()
			}
			return common.StringEmpty
		}(),
	}

	// 检查是否是最后一个节点，并确定工作流状态
	isLastNode := currentNodeRuntime.NodeIndex == int64(len(nodeRuntimes)-1)
	if validationError != nil {
		workflowState = auditCommon.WorkflowStateFailed
	} else if isLastNode {
		workflowState = auditCommon.WorkflowStatePass
	} else {
		workflowState = auditCommon.WorkflowStateProcessing
	}

	// 如果是最后一个节点或者有错误，更新工作流状态
	// 在事务中同时更新node与workflowRuntime状态
	err = workflowModel.ExecuteInTransaction(ctx, func(tx *gorm.DB) error {
		errNode := workflowModel.UpdateNodeRuntimeWithTx(ctx, tx, currentNodeRuntime.ID, currentNodeRuntime.Version, nodeData, &nodeState)
		if errNode != nil {
			return errNode
		}
		if isLastNode || validationError != nil {
			return workflowModel.UpdateWorkflowRuntimeWithTx(ctx, tx, workflowRuntime.ID, workflowRuntime.Version, nil, &workflowState)
		} else {
			return nil
		}
	})

	if err != nil {
		return nil, fmt.Errorf("failed to update flow runtime status: %v", err)
	}

	// 8. 返回flowInstanceID（如果有校验错误也返回错误）
	return &FlowInstanceItem{
		FlowName:       flowName,
		FlowInstanceID: flowInstanceID,
		FlowState:      auditCommon.FlowStateNameMap[workflowState],
		NodeState:      auditCommon.NodeStateNameMap[nodeState],
	}, validationError
}

/*
StartAuditFlow
创建flow的逻辑：

	1.根据flowName，查询对应的workflowTemplate（对应flowName的latest的template）和对应的nodeTemplate。
	2.校验传入的nodeName是否是对应的第一个nodeTemplate，且对应的nodeTemplate的systemID是否和这个系统的systemID对得上（systemID通过context.GetString(auditCommon.KeySystem)获取），如果不是，说明入参非法。
	3.创建workflowRuntime和对应的所有nodeRuntime，并将传入的data记入第一个nodeRuntime里，这里需要用一个整体事务来完成.
	4.根据对应的nodeTemplate的nodeConfig，调用对应的ValidateSchema方法，校验data是否符合要求，如果不符合，返回错误。
	5.再根据nodeConfig里的rule，校验当前节点的data是否符合要求，这里校验时需要把本节点，以及前序所有节点的data都set进一个data map里，key是对应的nodeName，value是对应节点的data，最后再调用ValidateData进行校验，如果出错就说明数据非法。
	6.如果到这里还没出问题，则说明数据合法，此时将nodeRuntime的状态设置为auditCommon.NodeStatePassed，并返回flowInstanceID。
*/
func StartAuditFlow(ctx context.Context,
	flowName string, nodeName string, data interface{}) (instance *FlowInstanceItem, err error) {

	// 1. 根据flowName查询对应的workflowTemplate和nodeTemplates（latest版本）
	workflowTemplate, nodeTemplates, err := workflowModel.GetLatestWorkflowAndNodeTmplByFlowName(ctx, flowName)
	if err != nil {
		return nil, fmt.Errorf("failed to get latest flow template for flow '%s': %v", flowName, err)
	}

	// 2. 校验传入的nodeName是否是第一个nodeTemplate
	firstNodeTemplate := nodeTemplates[0]
	if firstNodeTemplate.NodeName != nodeName {
		return nil, fmt.Errorf("invalid node name '%s', expected first node '%s'", nodeName, firstNodeTemplate.NodeName)
	}

	// 校验systemID是否匹配
	systemIDStr, ok := ctx.Value(auditCommon.KeySystem).(string)
	if !ok || systemIDStr == common.StringEmpty {
		return nil, fmt.Errorf("system ID not found")
	}

	if firstNodeTemplate.SystemID != systemIDStr {
		return nil, fmt.Errorf("system ID mismatch: expected '%s', got '%s'", firstNodeTemplate.SystemID, systemIDStr)
	}

	// 4. 进入schema校验阶段，从这里开始无论成功失败都要记录完整流程
	var validationError error
	nodeState := auditCommon.NodeStatePass
	workflowState := auditCommon.WorkflowStateInit

	// Schema校验
	if firstNodeTemplate.Config != nil && firstNodeTemplate.Config.Schema != nil {
		if err := firstNodeTemplate.Config.ValidateSchema(data); err != nil {
			validationError = fmt.Errorf("data validation failed against schema: %v", err)
			nodeState = auditCommon.NodeStateFailed
			workflowState = auditCommon.WorkflowStateFailed
		}
	}

	// 5. 根据nodeConfig的rules校验data（只有schema校验通过才继续）
	if validationError == nil && firstNodeTemplate.Config != nil && len(firstNodeTemplate.Config.Rules) > 0 {
		// 构造数据map（当前只有第一个节点的数据）
		dataMap := map[string]interface{}{
			nodeName: data,
		}

		// 校验数据
		if err := firstNodeTemplate.Config.ValidateData(dataMap); err != nil {
			validationError = fmt.Errorf("data validation failed against rules: %v", err)
			nodeState = auditCommon.NodeStateFailed
			workflowState = auditCommon.WorkflowStateFailed
		}
	}

	// 如果校验通过且是最后一个节点，设置工作流为完成状态
	if validationError == nil && len(nodeTemplates) == 1 {
		workflowState = auditCommon.WorkflowStatePass
	} else if validationError == nil {
		workflowState = auditCommon.WorkflowStateProcessing
	}

	// 3. 在事务中创建workflowRuntime和所有nodeRuntime（无论校验成功失败都要记录）
	var flowInstanceID int64
	err = workflowModel.ExecuteInTransaction(ctx, func(tx *gorm.DB) error {
		return createWorkflowAndNodesWithValidation(ctx, tx, workflowTemplate, nodeTemplates, nodeName, data, validationError, nodeState, workflowState, &flowInstanceID)
	})

	if err != nil {
		return nil, fmt.Errorf("failed to create flow and nodes: %v", err)
	}

	return &FlowInstanceItem{
		FlowInstanceID: flowInstanceID,
		FlowState:      auditCommon.FlowStateNameMap[workflowState],
		NodeState:      auditCommon.NodeStateNameMap[nodeState],
	}, validationError
}

// createWorkflowAndNodesWithValidation 在事务中创建workflow和所有nodes，包含验证结果
func createWorkflowAndNodesWithValidation(ctx context.Context, tx *gorm.DB,
	workflowInstanceID string,
	workflowTemplate *workflowModel.AuditWorkflowTemplateTable,
	nodeTemplates []*workflowModel.AuditNodeTemplateTable,
	firstNodeName string, firstNodeData interface{},
	validationError error, nodeState auditCommon.NodeState, workflowState auditCommon.WorkflowState,
	flowInstanceID *int64) error {

	// 创建workflowRuntime
	workflowRuntimeID, err := workflowModel.CreateWorkflowRuntimeInTx(ctx, tx, workflowTemplate, workflowState)
	if err != nil {
		return fmt.Errorf("failed to create flow runtime: %v", err)
	}

	*flowInstanceID = int64(workflowRuntimeID)

	// 创建所有nodeRuntime
	for _, nodeTemplate := range nodeTemplates {
		var nodeData *workflowModel.NodeData
		var currentNodeState auditCommon.NodeState

		if nodeTemplate.NodeName == firstNodeName {
			// 第一个节点，设置传入的数据和验证结果
			nodeData = &workflowModel.NodeData{
				Data: firstNodeData,
				Error: func() string {
					if validationError != nil {
						return validationError.Error()
					}
					return common.StringEmpty
				}(),
			}
			currentNodeState = nodeState
		} else {
			// 其他节点，初始状态
			nodeData = &workflowModel.NodeData{
				Data: nil,
			}
			currentNodeState = auditCommon.NodeStateInit
		}

		_, err := workflowModel.CreateNodeRuntimeInTx(ctx, tx, int64(workflowRuntimeID), nodeTemplate, nodeData, currentNodeState)
		if err != nil {
			return fmt.Errorf("failed to create node runtime for node '%s': %v", nodeTemplate.NodeName, err)
		}
	}

	return nil
}
