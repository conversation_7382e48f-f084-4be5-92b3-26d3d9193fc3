/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-07-04
 */
package template

import (
	"time"

	flowModel "gitlab.docsl.com/security/audit/internal/model/flow"
)

type WorkflowTemplateItem struct {
	WorkflowTemplateID int64               `json:"workflowTemplateID"`
	WorkflowName       string              `json:"workflowName"`
	Description        string              `json:"description"`
	CreatedAt          time.Time           `json:"createdAt"`
	UpdatedAt          time.Time           `json:"updatedAt"`
	NodeTemplates      []*NodeTemplateItem `json:"nodeTemplates"`
}

type NodeTemplateItem struct {
	NodeTemplateID int64                 `json:"nodeTemplateID"`
	NodeName       string                `json:"nodeName" validate:"required"`
	Description    string                `json:"description"`
	NodeIndex      int64                 `json:"nodeIndex" validate:"required"`
	SystemID       string                `json:"systemID" validate:"required"`
	Config         *flowModel.NodeConfig `json:"config" validate:"required"`
}

// ConvertToWorkflowTemplateItem 转换为WorkflowTemplateItem
func ConvertToWorkflowTemplateItem(wt *flowModel.AuditWorkflowTemplateTable, nodeTemplates []*flowModel.AuditNodeTemplateTable) *WorkflowTemplateItem {
	nodeItems := make([]*NodeTemplateItem, len(nodeTemplates))
	for i, nt := range nodeTemplates {
		nodeItems[i] = &NodeTemplateItem{
			NodeTemplateID: int64(nt.ID),
			NodeName:       nt.NodeName,
			Description:    nt.Description,
			NodeIndex:      nt.NodeIndex,
			SystemID:       nt.SystemID,
			Config:         nt.Config,
		}
	}

	return &WorkflowTemplateItem{
		WorkflowTemplateID: int64(wt.ID),
		WorkflowName:       wt.WorkflowName,
		Description:        wt.Description,
		CreatedAt:          wt.CreatedAt,
		UpdatedAt:          wt.UpdatedAt,
		NodeTemplates:      nodeItems,
	}
}

// ConvertToWorkflowTemplateItemList 转换为响应列表格式
func ConvertToWorkflowTemplateItemList(workflowTemplates []*flowModel.AuditWorkflowTemplateTable, nodeTemplatesMap map[int64][]*flowModel.AuditNodeTemplateTable) []*WorkflowTemplateItem {
	result := make([]*WorkflowTemplateItem, len(workflowTemplates))
	for i, wt := range workflowTemplates {
		nodeTemplates := nodeTemplatesMap[int64(wt.ID)]
		result[i] = ConvertToWorkflowTemplateItem(wt, nodeTemplates)
	}
	return result
}
