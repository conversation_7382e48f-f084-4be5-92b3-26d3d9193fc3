/**
 * @note
 * template logic
 *
 * <AUTHOR>
 * @date 	2025-07-04
 */
package template

import (
	"context"
	"fmt"

	flowModel "gitlab.docsl.com/security/audit/internal/model/flow"
	auditCommon "gitlab.docsl.com/security/audit/pkg/common"
	"gitlab.docsl.com/security/audit/pkg/model/op_log"
	"gorm.io/gorm"
)

// ListWorkflowTemplates 查询流程模板列表（只查询is_latest=true的）
func ListWorkflowTemplates(ctx context.Context, page int, perPage int) ([]*WorkflowTemplateItem, int64, error) {
	// 构造查询过滤器，只查询最新版本
	isLatest := true
	filter := flowModel.QueryWorkflowTemplateFilter{
		Page:     page,
		PerPage:  perPage,
		IsLatest: &isLatest,
		Order:    "created_at DESC",
	}

	// 查询流程模板列表
	workflowTemplates, err := flowModel.QueryWorkflowTemplatesBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// 查询总数
	total, err := flowModel.QueryWorkflowTemplatesCountBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// 构造节点模板映射
	nodeTemplatesMap := make(map[int64][]*flowModel.AuditNodeTemplateTable)
	for _, wt := range workflowTemplates {
		templateID := int64(wt.ID)
		nodeTemplateFilter := flowModel.QueryNodeTemplateFilter{
			WorkflowTemplateID: &templateID,
		}
		nodeTemplates, err := flowModel.QueryNodeTemplatesBySeveralConditions(ctx, nodeTemplateFilter)
		if err != nil {
			return nil, 0, err
		}
		nodeTemplatesMap[templateID] = nodeTemplates
	}

	// 转换为响应格式
	items := ConvertToWorkflowTemplateItemList(workflowTemplates, nodeTemplatesMap)

	return items, total, nil
}

// CreateWorkflowTemplate 创建流程模板
func CreateWorkflowTemplate(ctx context.Context, workflowName string, description string, nodeTemplates []*NodeTemplateItem) (int64, error) {
	var workflowTemplateID int64

	// 在事务中执行创建操作
	err := flowModel.ExecuteInTransaction(ctx, func(tx *gorm.DB) error {
		// 1. 创建流程模板
		nodeCount := int64(len(nodeTemplates))
		id, err := flowModel.CreateWorkflowTemplate(ctx, workflowName, description, nodeCount, true)
		if err != nil {
			return fmt.Errorf("failed to create workflow template: %v", err)
		}
		workflowTemplateID = int64(id)

		// 2. 创建节点模板
		for i, nodeTemplate := range nodeTemplates {
			_, err := flowModel.CreateNodeTemplate(ctx,
				nodeTemplate.NodeName,
				workflowTemplateID,
				nodeTemplate.Description,
				int64(i), // nodeIndex从0开始
				nodeTemplate.SystemID,
				nodeTemplate.Config)
			if err != nil {
				return fmt.Errorf("failed to create node template %s: %v", nodeTemplate.NodeName, err)
			}
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	// 记录操作日志
	_ = op_log.CreateOperationLog(ctx, auditCommon.OperationTemplateCreate, &op_log.OperationDetail{
		Target: map[string]interface{}{
			"workflowName":  workflowName,
			"description":   description,
			"nodeTemplates": nodeTemplates,
		},
		After: map[string]interface{}{
			"workflowTemplateID": workflowTemplateID,
		},
	})

	return workflowTemplateID, nil
}

// UpdateWorkflowTemplate 更新流程模板（实际上是创建新版本）
func UpdateWorkflowTemplate(ctx context.Context, workflowName string, description *string, nodeTemplates []*NodeTemplateItem) (int64, error) {
	var newWorkflowTemplateID int64

	// 在事务中执行更新操作
	err := flowModel.ExecuteInTransaction(ctx, func(tx *gorm.DB) error {
		// 1. 将同名的旧版本模板的is_latest设为false
		oldTemplates, err := flowModel.QueryWorkflowTemplatesBySeveralConditions(ctx, flowModel.QueryWorkflowTemplateFilter{
			WorkflowName: workflowName,
		})
		if err != nil {
			return fmt.Errorf("failed to query existing templates: %v", err)
		}

		for _, oldTemplate := range oldTemplates {
			isLatest := false
			err := flowModel.UpdateWorkflowTemplate(ctx, oldTemplate.ID, nil, nil, &isLatest)
			if err != nil {
				return fmt.Errorf("failed to update old template is_latest: %v", err)
			}
		}

		// 2. 创建新的流程模板
		nodeCount := int64(len(nodeTemplates))
		desc := ""
		if description != nil {
			desc = *description
		}

		id, err := flowModel.CreateWorkflowTemplate(ctx, workflowName, desc, nodeCount, true)
		if err != nil {
			return fmt.Errorf("failed to create new workflow template: %v", err)
		}
		newWorkflowTemplateID = int64(id)

		// 3. 创建新的节点模板
		for i, nodeTemplate := range nodeTemplates {
			_, err := flowModel.CreateNodeTemplate(ctx,
				nodeTemplate.NodeName,
				newWorkflowTemplateID,
				nodeTemplate.Description,
				int64(i), // nodeIndex从0开始
				nodeTemplate.SystemID,
				nodeTemplate.Config)
			if err != nil {
				return fmt.Errorf("failed to create node template %s: %v", nodeTemplate.NodeName, err)
			}
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	// 记录操作日志
	_ = op_log.CreateOperationLog(ctx, auditCommon.OperationTemplateUpdate, &op_log.OperationDetail{
		Target: map[string]interface{}{"workflowName": workflowName},
		After: map[string]interface{}{
			"workflowTemplateID": newWorkflowTemplateID,
		},
	})

	return newWorkflowTemplateID, nil
}

// DeleteWorkflowTemplate 删除流程模板（实际上是将is_latest设为false）
func DeleteWorkflowTemplate(ctx context.Context, workflowName string) error {
	// 查询要删除的模板
	templates, err := flowModel.QueryWorkflowTemplatesBySeveralConditions(ctx, flowModel.QueryWorkflowTemplateFilter{
		WorkflowName: workflowName,
	})
	if err != nil {
		return fmt.Errorf("failed to query templates: %v", err)
	}

	if len(templates) == 0 {
		return fmt.Errorf("template with name '%s' not found", workflowName)
	}

	// 记录删除前的状态用于日志
	var beforeData []*WorkflowTemplateItem
	for _, template := range templates {
		if template.IsLatest {
			templateID := int64(template.ID)
			nodeTemplateFilter := flowModel.QueryNodeTemplateFilter{
				WorkflowTemplateID: &templateID,
			}
			nodeTemplates, err := flowModel.QueryNodeTemplatesBySeveralConditions(ctx, nodeTemplateFilter)
			if err != nil {
				return fmt.Errorf("failed to query node templates: %v", err)
			}
			beforeData = append(beforeData, ConvertToWorkflowTemplateItem(template, nodeTemplates))
		}
	}

	// 将所有同名模板的is_latest设为false
	for _, template := range templates {
		isLatest := false
		err := flowModel.UpdateWorkflowTemplate(ctx, template.ID, nil, nil, &isLatest)
		if err != nil {
			return fmt.Errorf("failed to update template is_latest: %v", err)
		}
	}

	// 记录操作日志
	_ = op_log.CreateOperationLog(ctx, auditCommon.OperationTemplateDelete, &op_log.OperationDetail{
		Target: map[string]interface{}{"workflowName": workflowName},
		Before: beforeData,
	})

	return nil
}
