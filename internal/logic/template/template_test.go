package template

import (
	"testing"

	flowModel "gitlab.docsl.com/security/audit/internal/model/flow"
)

func TestConvertToWorkflowTemplateItem(t *testing.T) {
	// 测试转换函数
	wt := &flowModel.AuditWorkflowTemplateTable{
		WorkflowName: "test-workflow",
		Description:  "Test workflow description",
		NodeCount:    2,
		IsLatest:     true,
	}
	wt.ID = 1

	nodeTemplates := []*flowModel.AuditNodeTemplateTable{
		{
			NodeName:           "node1",
			WorkflowTemplateID: 1,
			Description:        "First node",
			NodeIndex:          0,
			SystemID:           "system1",
			Config:             &flowModel.NodeConfig{},
		},
		{
			NodeName:           "node2",
			WorkflowTemplateID: 1,
			Description:        "Second node",
			NodeIndex:          1,
			SystemID:           "system2",
			Config:             &flowModel.NodeConfig{},
		},
	}
	nodeTemplates[0].ID = 1
	nodeTemplates[1].ID = 2

	result := ConvertToWorkflowTemplateItem(wt, nodeTemplates)

	if result.WorkflowTemplateID != 1 {
		t.<PERSON>rrorf("Expected WorkflowTemplateID 1, got %d", result.WorkflowTemplateID)
	}

	if result.WorkflowName != "test-workflow" {
		t.Errorf("Expected WorkflowName 'test-workflow', got %s", result.WorkflowName)
	}

	if len(result.NodeTemplates) != 2 {
		t.Errorf("Expected 2 node templates, got %d", len(result.NodeTemplates))
	}

	if result.NodeTemplates[0].NodeName != "node1" {
		t.Errorf("Expected first node name 'node1', got %s", result.NodeTemplates[0].NodeName)
	}

	if result.NodeTemplates[1].NodeIndex != 1 {
		t.Errorf("Expected second node index 1, got %d", result.NodeTemplates[1].NodeIndex)
	}
}

func TestNodeTemplateItem(t *testing.T) {
	// 测试NodeTemplateItem结构体
	item := &NodeTemplateItem{
		NodeTemplateID: 1,
		NodeName:       "test-node",
		Description:    "Test node description",
		NodeIndex:      0,
		SystemID:       "system1",
		Config:         &flowModel.NodeConfig{},
	}

	if item.NodeTemplateID != 1 {
		t.Errorf("Expected NodeTemplateID 1, got %d", item.NodeTemplateID)
	}

	if item.NodeName != "test-node" {
		t.Errorf("Expected NodeName 'test-node', got %s", item.NodeName)
	}

	if item.NodeIndex != 0 {
		t.Errorf("Expected NodeIndex 0, got %d", item.NodeIndex)
	}
}

func TestWorkflowTemplateItem(t *testing.T) {
	// 测试WorkflowTemplateItem结构体
	nodeTemplates := []*NodeTemplateItem{
		{
			NodeTemplateID: 1,
			NodeName:       "node1",
			Description:    "First node",
			NodeIndex:      0,
			SystemID:       "system1",
			Config:         &flowModel.NodeConfig{},
		},
	}

	item := &WorkflowTemplateItem{
		WorkflowTemplateID: 1,
		WorkflowName:       "test-workflow",
		Description:        "Test description",
		NodeTemplates:      nodeTemplates,
	}

	if item.WorkflowTemplateID != 1 {
		t.Errorf("Expected WorkflowTemplateID 1, got %d", item.WorkflowTemplateID)
	}

	if item.WorkflowName != "test-workflow" {
		t.Errorf("Expected WorkflowName 'test-workflow', got %s", item.WorkflowName)
	}

	if len(item.NodeTemplates) != 1 {
		t.Errorf("Expected 1 node template, got %d", len(item.NodeTemplates))
	}
}
