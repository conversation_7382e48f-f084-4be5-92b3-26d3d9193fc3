/**
 * @note
 * workflow structs
 *
 * <AUTHOR>
 * @date 	2025-06-26
 */
package flow

import (
	"time"

	"gorm.io/gorm"

	auditCommon "gitlab.docsl.com/security/audit/pkg/common"
)

// AuditWorkflowTemplateTable 流程模板表
type AuditWorkflowTemplateTable struct {
	gorm.Model
	WorkflowName string `gorm:"column:workflow_name"` // 流程模板的标识
	Description  string `gorm:"column:description"`   // 描述
	NodeCount    int64  `gorm:"column:node_count"`    // 包含节点数量
	IsLatest     bool   `gorm:"column:is_latest"`     // 0: 历史版本, 1: 最新版本
}

func (t *AuditWorkflowTemplateTable) TableName() string {
	return auditCommon.WorkflowTemplateTableName
}

// AuditNodeTemplateTable 流程模板节点表
type AuditNodeTemplateTable struct {
	gorm.Model
	NodeName           string      `gorm:"column:node_name"`            // 流程模板节点的标识
	WorkflowTemplateID int64       `gorm:"column:workflow_template_id"` // 所属流程模板的主键ID
	Description        string      `gorm:"column:description"`          // 描述
	NodeIndex          int64       `gorm:"column:node_index"`           // 节点序号，从0开始
	SystemID           string      `gorm:"column:system_id"`            // 所属系统的ID
	Config             *NodeConfig `gorm:"column:config"`               // 节点配置
}

func (t *AuditNodeTemplateTable) TableName() string {
	return auditCommon.NodeTemplateTableName
}

// AuditWorkflowRuntimeTable 流程运行表
type AuditWorkflowRuntimeTable struct {
	gorm.Model
	WorkflowName       string                    `gorm:"column:workflow_name"`        // 流程模板的标识
	WorkflowTemplateID int64                     `gorm:"column:workflow_template_id"` // 所属流程模板的主键ID
	NodeCount          int64                     `gorm:"column:node_count"`           // 包含节点数量
	Version            int64                     `gorm:"column:version"`              // 版本号，UnixMicro
	State              auditCommon.WorkflowState `gorm:"column:state"`                // 节点状态，0:初始化，1:执行中，2:校验通过，3:校验失败
}

func (t *AuditWorkflowRuntimeTable) TableName() string {
	return auditCommon.WorkflowRuntimeTableName
}

// AuditNodeRuntimeTable 流程运行节点表
type AuditNodeRuntimeTable struct {
	gorm.Model
	WorkflowInstanceID string                `gorm:"column:workflow_instance_id"` // 所属workflow_runtime的主键ID
	NodeName           string                `gorm:"column:node_name"`            // 流程节点的标识
	NodeTemplateID     int64                 `gorm:"column:node_template_id"`     // 所属流程模板节点的主键ID
	SystemID           string                `gorm:"column:system_id"`            // 所属系统的ID
	NodeIndex          int64                 `gorm:"column:node_index"`           // 节点序号，从0开始
	Version            int64                 `gorm:"column:version"`              // 版本号，UnixMicro
	Data               *NodeData             `gorm:"column:data"`                 // 节点数据
	State              auditCommon.NodeState `gorm:"column:state"`                // 节点状态，0:初始化，1:通过，2:校验失败
}

func (t *AuditNodeRuntimeTable) TableName() string {
	return auditCommon.NodeRuntimeTableName
}

// 查询过滤器

// QueryWorkflowTemplateFilter 查询流程模板的过滤条件
type QueryWorkflowTemplateFilter struct {
	Page, PerPage int
	WorkflowName  string `json:"workflowName"`
	IsLatest      *bool  `json:"isLatest"`
	Order         string `json:"order"`
}

// QueryNodeTemplateFilter 查询节点模板的过滤条件
type QueryNodeTemplateFilter struct {
	Page, PerPage      int
	WorkflowTemplateID *int64 `json:"workflowTemplateID"`
	NodeName           string `json:"nodeName"`
	SystemID           string `json:"systemID"`
	Order              string `json:"order"`
}

// QueryWorkflowRuntimeFilter 查询流程运行的过滤条件
type QueryWorkflowRuntimeFilter struct {
	Page, PerPage      int
	WorkflowName       string `json:"workflowName"`
	WorkflowTemplateID *int64 `json:"workflowTemplateID"`
	Order              string `json:"order"`
}

// QueryNodeRuntimeFilter 查询节点运行的过滤条件
type QueryNodeRuntimeFilter struct {
	Page, PerPage      int
	WorkflowInstanceID *int64                 `json:"workflowInstanceID"`
	NodeName           string                 `json:"nodeName"`
	SystemID           string                 `json:"systemID"`
	State              *auditCommon.NodeState `json:"state"`
	StartTime          *time.Time             `json:"startTime"`
	EndTime            *time.Time             `json:"endTime"`
	Order              string                 `json:"order"`
}
