/**
 * @note
 * system model
 *
 * <AUTHOR>
 * @date 	2025-06-26
 */
package system

import (
	"context"

	"gorm.io/gorm"

	auditCommon "gitlab.docsl.com/security/audit/pkg/common"
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
	"gitlab.docsl.com/security/common/mysql"
)

type SystemModel interface {
	// GetSystemBySystemID 根据系统ID查找系统
	GetSystemBySystemID(ctx context.Context, systemID string) (*AuditSystemTable, error)
	// GetSystemByPublicKey 根据API Key查找系统
	GetSystemByPublicKey(ctx context.Context, apiKey string) (*AuditSystemTable, error)
	// QuerySystemsBySeveralConditions 根据多个条件查询系统列表
	QuerySystemsBySeveralConditions(ctx context.Context, filter QuerySystemFilter) ([]*AuditSystemTable, error)
	// QuerySystemsCountBySeveralConditions 根据多个条件查询系统总数
	QuerySystemsCountBySeveralConditions(ctx context.Context, filter QuerySystemFilter) (int64, error)
	// CreateSystem 创建系统
	CreateSystem(ctx context.Context, systemID, systemName, description, keyType, publicKey string) (id uint, err error)
	// UpdateSystem 更新系统
	UpdateSystem(ctx context.Context, systemID string, systemName, description, keyType, publicKey *string) error
	// DeleteSystem 删除系统
	DeleteSystem(ctx context.Context, systemID string) error
}

var DefaultService SystemModel = &SystemModelImpl{}

type SystemModelImpl struct{}

func (m *SystemModelImpl) getDB(ctx context.Context) (db *gorm.DB, err error) {
	return mysql.GetDB(auditCommon.DBName, false, common.GetLogger(ctx))
}

func (m *SystemModelImpl) GetSystemBySystemID(ctx context.Context, systemID string) (*AuditSystemTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&AuditSystemTable{})
	ret := &AuditSystemTable{}
	db = db.Where("system_id = ?", systemID).First(ret)
	return ret, db.Error
}

func (m *SystemModelImpl) GetSystemByPublicKey(ctx context.Context, pubKey string) (*AuditSystemTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&AuditSystemTable{})
	ret := &AuditSystemTable{}
	db = db.Where("public_key = ?", pubKey).First(ret)
	return ret, db.Error
}

func assembleSystemQueryConditions(ctx context.Context, filter QuerySystemFilter, db *gorm.DB) *gorm.DB {
	if filter.SystemID != "" {
		db = db.Where("system_id = ?", filter.SystemID)
	}
	if filter.SystemName != "" {
		db = db.Where("system_name = ?", filter.SystemName)
	}
	if filter.KeyType != "" {
		db = db.Where("key_type = ?", filter.KeyType)
	}

	// 排序
	if filter.Order != common.StringEmpty {
		db = db.Order(filter.Order)
	}

	return db
}

func (m *SystemModelImpl) QuerySystemsBySeveralConditions(ctx context.Context, filter QuerySystemFilter) ([]*AuditSystemTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&AuditSystemTable{})

	db = assembleSystemQueryConditions(ctx, filter, db)

	// 分页
	if filter.PerPage > 0 {
		db = db.Limit(filter.PerPage)
	}
	if filter.Page > 0 {
		db = db.Offset((filter.Page - 1) * filter.PerPage)
	}

	var results []*AuditSystemTable
	db = db.Find(&results)
	return results, db.Error
}

func (m *SystemModelImpl) QuerySystemsCountBySeveralConditions(ctx context.Context, filter QuerySystemFilter) (count int64, err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&AuditSystemTable{})

	db = assembleSystemQueryConditions(ctx, filter, db)
	db = db.Count(&count)
	return count, db.Error
}

func (m *SystemModelImpl) CreateSystem(ctx context.Context, systemID, systemName, description, keyType, publicKey string) (id uint, err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}

	generatedID := idgen.GetID()
	tb := &AuditSystemTable{
		SystemID:    systemID,
		SystemName:  systemName,
		Description: description,
		KeyType:     keyType,
		PublicKey:   publicKey,
	}
	tb.ID = uint(generatedID)

	db = db.Create(tb)
	return tb.ID, db.Error
}

func (m *SystemModelImpl) UpdateSystem(ctx context.Context, systemID string, systemName, description, keyType, publicKey *string) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&AuditSystemTable{})

	updateMap := make(map[string]interface{})
	if systemName != nil {
		updateMap["system_name"] = *systemName
	}
	if description != nil {
		updateMap["description"] = *description
	}
	if keyType != nil {
		updateMap["key_type"] = *keyType
	}
	if publicKey != nil {
		updateMap["public_key"] = *publicKey
	}

	if len(updateMap) == 0 {
		return nil
	}

	db = db.Where("system_id = ?", systemID).Updates(updateMap)
	return db.Error
}

func (m *SystemModelImpl) DeleteSystem(ctx context.Context, systemID string) error {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&AuditSystemTable{})
	db = db.Where("system_id = ?", systemID).Delete(&AuditSystemTable{})
	return db.Error
}

func GetSystemBySystemID(ctx context.Context, systemID string) (*AuditSystemTable, error) {
	return DefaultService.GetSystemBySystemID(ctx, systemID)
}

func GetSystemByPublicKey(ctx context.Context, apiKey string) (*AuditSystemTable, error) {
	return DefaultService.GetSystemByPublicKey(ctx, apiKey)
}

func QuerySystemsBySeveralConditions(ctx context.Context, filter QuerySystemFilter) ([]*AuditSystemTable, error) {
	return DefaultService.QuerySystemsBySeveralConditions(ctx, filter)
}

func QuerySystemsCountBySeveralConditions(ctx context.Context, filter QuerySystemFilter) (int64, error) {
	return DefaultService.QuerySystemsCountBySeveralConditions(ctx, filter)
}

func CreateSystem(ctx context.Context, systemID, systemName, description, keyType, publicKey string) (uint, error) {
	return DefaultService.CreateSystem(ctx, systemID, systemName, description, keyType, publicKey)
}

func UpdateSystem(ctx context.Context, systemID string, systemName, description, keyType, publicKey *string) error {
	return DefaultService.UpdateSystem(ctx, systemID, systemName, description, keyType, publicKey)
}

func DeleteSystem(ctx context.Context, systemID string) error {
	return DefaultService.DeleteSystem(ctx, systemID)
}
